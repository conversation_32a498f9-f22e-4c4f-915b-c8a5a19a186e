const BASE_CACHE = require("@/utils/cache/baseCache")

/**
 * 从完整URL中提取基础路径（不包含查询参数）
 * @param {string} url - 完整的页面URL
 * @returns {string} - 基础路径
 */
function getBasePath(url) {
  if (!url) return ""
  return url.split("?")[0]
}

/**
 * 清理页面栈中的重复路径，只保留每个路径的最新实例
 * 当页面栈超过6个页面时触发清理
 */
function cleanupPageStack() {
  const pages = getCurrentPages()

  // 如果页面栈长度不超过6个，不需要清理
  if (pages.length <= 6) {
    return false
  }

  console.log("页面栈超过6个，开始分析重复路径", pages.length)

  // 记录每个基础路径的出现次数和位置
  const pathOccurrences = new Map()

  // 遍历页面栈，记录每个路径的所有出现位置
  pages.forEach((page, index) => {
    const basePath = getBasePath(page.route)
    if (!pathOccurrences.has(basePath)) {
      pathOccurrences.set(basePath, [])
    }
    pathOccurrences.get(basePath).push(index)
  })

  // 找出有重复的路径
  const duplicatePaths = Array.from(pathOccurrences.entries()).filter(
    ([path, indices]) => indices.length > 1
  )

  if (duplicatePaths.length === 0) {
    console.log("页面栈中没有重复路径")
    return false
  }

  console.log(
    "发现重复路径:",
    duplicatePaths.map(([path, indices]) => `${path} (出现${indices.length}次)`)
  )

  // 计算需要返回的步数来清理最近的重复页面
  // 策略：从当前页面开始往前查找，找到第一个重复的页面，返回到它的前一个不重复位置
  const currentIndex = pages.length - 1
  let backSteps = 0

  // 从当前页面往前查找重复页面
  for (let i = currentIndex; i >= 1; i--) {
    const currentPath = getBasePath(pages[i].route)
    const occurrences = pathOccurrences.get(currentPath)

    if (occurrences && occurrences.length > 1) {
      // 找到重复页面，计算返回步数
      // 返回到这个路径第一次出现的位置，这样会清理掉后面的重复实例
      const firstOccurrence = occurrences[0]
      if (firstOccurrence < i) {
        backSteps = currentIndex - firstOccurrence
        console.log(
          `路径 ${currentPath} 重复出现，返回 ${backSteps} 步清理重复页面`
        )
        break
      }
    }
  }

  // 如果计算出需要返回的步数，执行清理
  if (backSteps > 0 && backSteps < pages.length) {
    try {
      console.log(`执行页面栈清理：返回 ${backSteps} 步`)
      wx.navigateBack({
        delta: backSteps,
      })
      return true // 表示执行了清理操作
    } catch (error) {
      console.error("页面栈清理失败:", error)
    }
  } else {
    console.log("无法确定安全的清理方案，跳过清理")
  }

  return false // 表示没有执行清理操作
}

/**
 * 手动触发页面栈清理
 * 可以在需要的时候主动调用此函数来清理页面栈
 * @returns {boolean} 是否执行了清理操作
 */
export function manualCleanupPageStack() {
  console.log("手动触发页面栈清理")
  return cleanupPageStack()
}

export function createPathWithParams(path, params) {
  if (!params || typeof params !== "object") return path
  const query = convertPathQuery(params, convertQueryParamValue)
  return query ? `${path}?${query}` : path
}

/**
 * 转换单个参数值，使其适合URL查询字符串。
 * @param {string|object} val - 参数的值。
 * @returns {string} - 转换后的值。
 */
function convertQueryParamValue(val) {
  if (typeof val === "object") {
    return JSON.stringify(val)
  } else if (typeof val === "string" && val?.indexOf("://") >= 0) {
    return encodeURIComponent(val)
  }
  return val
}

/**
 * 遍历参数对象，并使用convertQueryParamValue函数转换每个值。
 * @param {Object} params - 包含查询参数的对象。
 * @returns {string} - 转换后的查询字符串。
 */
export function convertPathQuery(params, call) {
  return Object.keys(params)
    .map((key) => {
      if (typeof call === "function") {
        return `${key}=${call(params[key])}`
      }
      return `${key}=${params[key]}`
    })
    .join("&")
}
export function mergeUrlParams(url, params) {
  // 拆分URL为路径和查询部分
  const [path, queryString] = url.split("?")

  // 解析原有的查询参数
  const searchParams = {}
  if (queryString) {
    queryString.split("&").forEach((pair) => {
      const [key, value] = pair.split("=")
      if (key) {
        // 解码键和值
        const decodedKey = decodeURIComponent(key)
        const decodedValue = decodeURIComponent(value || "")
        searchParams[decodedKey] = decodedValue
      }
    })
  }

  // 过滤掉params中值为undefined或null的参数
  const filteredParams = {}
  Object.keys(params).forEach((key) => {
    const value = params[key]
    if (value !== undefined && value !== null) {
      filteredParams[key] = value
    }
  })

  // 合并参数，后者覆盖前者
  const mergedParams = { ...searchParams, ...filteredParams }

  // 将参数转换为查询字符串
  const queryArray = Object.keys(mergedParams).map((key) => {
    // 编码键和值
    const encodedKey = encodeURIComponent(key)
    const encodedValue = encodeURIComponent(mergedParams[key])
    return `${encodedKey}=${encodedValue}`
  })

  const newQueryString = queryArray.join("&")

  // 拼接路径和查询字符串
  return newQueryString ? `${path}?${newQueryString}` : path
}

function navigate({ api, data = {} }) {
  const { path, query } = data

  // 检查路径是否有效
  if (!path && api !== "navigateBack") throw new Error("路径传参有误")
  const defaultQuery = {}
  const baseCache = BASE_CACHE.getBaseCache()
  if (baseCache.campus?.id) {
    defaultQuery.campus_id = baseCache.campus.id
  }
  if (baseCache.province?.key) {
    defaultQuery.province = baseCache.province.key
  }

  let newQuery = query
  if (!query?.province && !Number(query?.campus_id)) {
    newQuery = Object.assign(defaultQuery, query || {})
  }

  // 构建完整的URL
  const url = createPathWithParams(path, newQuery)

  return wx[api]({ url })
}

export function navigateTo(data = {}) {
  // 在导航前清理页面栈
  const isCleaningUp = cleanupPageStack()

  // 如果正在清理页面栈，则不执行当前导航
  // 因为页面栈重建过程中会处理导航
  if (isCleaningUp) {
    console.log("页面栈正在清理中，暂停当前导航")
    return Promise.resolve()
  }

  return navigate({ api: "navigateTo", data })
}

export function redirectTo(data = {}) {
  return navigate({ api: "redirectTo", data })
}

export function switchTab(data = {}) {
  return navigate({ api: "switchTab", data })
}

export function navigateBack(data = {}) {
  if (getCurrentPages().length <= 1) {
    reLaunch({ path: "/pages/index/index" })
    return
  }
  return navigate({ api: "navigateBack", data })
}

export function reLaunch(data = {}) {
  return navigate({ api: "reLaunch", data })
}
